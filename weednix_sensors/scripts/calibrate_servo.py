import cv2
import freenect
import numpy as np
import pyfirmata
import json
import time
from datetime import datetime

# Arduino setup
board = pyfirmata.Arduino('/dev/ttyACM0')  # Change path as needed
it = pyfirmata.util.Iterator(board)
it.start()

# Define servo and laser pins
servo_x = board.get_pin('d:10:s')     # D10: Servo X
servo_y = board.get_pin('d:9:s')      # D9: Servo Y
laser = board.get_pin('d:8:o')        # D8: Laser ON/OFF

# Servo ranges
SERVO_X_MIN = 70
SERVO_X_MAX = 120
SERVO_Y_MIN = 70
SERVO_Y_MAX = 122

# Camera dimensions
CAMERA_WIDTH = 640
CAMERA_HEIGHT = 480

# Calibration data storage
calibration_points = []
current_servo_x = 88  # Start at middle
current_servo_y = 85  # Start at middle
mouse_x, mouse_y = CAMERA_WIDTH // 2, CAMERA_HEIGHT // 2  # Mouse position

def get_kinect_frame():
    """Capture frame from Kinect v1"""
    _, _ = freenect.sync_get_depth()
    video, _ = freenect.sync_get_video()
    return cv2.cvtColor(video, cv2.COLOR_RGB2BGR)

def move_servo(x_angle, y_angle):
    """Move servo to specified angles"""
    x_angle = max(SERVO_X_MIN, min(SERVO_X_MAX, x_angle))
    y_angle = max(SERVO_Y_MIN, min(SERVO_Y_MAX, y_angle))
    servo_x.write(x_angle)
    servo_y.write(y_angle)
    time.sleep(0.1)  # Small delay for servo movement
    return x_angle, y_angle

def save_calibration_data():
    """Save calibration points to JSON file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"servo_calibration_{timestamp}.json"

    calibration_data = {
        "timestamp": timestamp,
        "servo_ranges": {
            "x_min": SERVO_X_MIN,
            "x_max": SERVO_X_MAX,
            "y_min": SERVO_Y_MIN,
            "y_max": SERVO_Y_MAX
        },
        "camera_dimensions": {
            "width": CAMERA_WIDTH,
            "height": CAMERA_HEIGHT
        },
        "calibration_points": calibration_points
    }

    with open(filename, 'w') as f:
        json.dump(calibration_data, f, indent=2)

    print(f"✅ Calibration data saved to: {filename}")
    print(f"📊 Total calibration points: {len(calibration_points)}")
    return filename

def mouse_callback(event, x, y, flags, param):
    """Mouse callback function to track mouse position"""
    global mouse_x, mouse_y
    # Update mouse position for any event (move, click, etc.)
    mouse_x, mouse_y = x, y

def load_latest_calibration():
    """Load the most recent calibration file"""
    import glob
    import os

    files = glob.glob("servo_calibration_*.json")
    if not files:
        print("❌ No calibration files found")
        return None

    latest_file = max(files, key=os.path.getctime)
    try:
        with open(latest_file, 'r') as f:
            data = json.load(f)
        print(f"📂 Loaded calibration from: {latest_file}")
        return data
    except Exception as e:
        print(f"❌ Error loading calibration: {e}")
        return None

def draw_interface(frame, servo_x_angle, servo_y_angle):
    """Draw calibration interface on frame"""
    # Draw crosshairs at center
    center_x = CAMERA_WIDTH // 2
    center_y = CAMERA_HEIGHT // 2
    cv2.line(frame, (center_x - 20, center_y), (center_x + 20, center_y), (0, 255, 0), 2)
    cv2.line(frame, (center_x, center_y - 20), (center_x, center_y + 20), (0, 255, 0), 2)

    # Draw mouse cursor position
    cv2.circle(frame, (mouse_x, mouse_y), 10, (0, 255, 255), 2)
    cv2.putText(frame, f"({mouse_x},{mouse_y})", (mouse_x + 15, mouse_y - 15),
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)

    # Draw existing calibration points
    for i, point in enumerate(calibration_points):
        cam_x, cam_y = point['camera']
        servo_x_val, servo_y_val = point['servo']

        # Draw point
        cv2.circle(frame, (int(cam_x), int(cam_y)), 8, (255, 0, 255), 2)
        cv2.putText(frame, f"{i+1}", (int(cam_x) + 10, int(cam_y) - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 255), 2)
        cv2.putText(frame, f"({servo_x_val},{servo_y_val})",
                   (int(cam_x) + 10, int(cam_y) + 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)

    # Draw current servo position info
    info_y = 30
    cv2.putText(frame, f"Servo Position: X={servo_x_angle}°, Y={servo_y_angle}°",
               (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

    cv2.putText(frame, f"Calibration Points: {len(calibration_points)}",
               (10, info_y + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

    # Instructions
    instructions = [
        "WASD: Move servo (W=up, S=down, A=left, D=right)",
        "SPACE: Add calibration point at mouse position",
        "L: Toggle laser on/off",
        "R: Reset calibration data",
        "Q: Save and quit",
        "ESC: Quit without saving"
    ]

    for i, instruction in enumerate(instructions):
        cv2.putText(frame, instruction, (10, CAMERA_HEIGHT - 150 + i * 25),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

def main():
    global current_servo_x, current_servo_y, calibration_points

    print("🎯 SERVO CALIBRATION TOOL")
    print("=" * 50)

    # Ask if user wants to load existing calibration
    load_existing = input("Load existing calibration data? (y/n): ").lower().strip()
    if load_existing == 'y':
        data = load_latest_calibration()
        if data and 'calibration_points' in data:
            calibration_points = data['calibration_points']

    # Initialize servo position
    move_servo(current_servo_x, current_servo_y)
    laser_on = False

    print("\n🎮 CONTROLS:")
    print("WASD: Move servo")
    print("SPACE: Add calibration point")
    print("L: Toggle laser")
    print("R: Reset calibration")
    print("Q: Save and quit")
    print("ESC: Quit without saving")
    print("\n▶️ Starting calibration...")

    while True:
        frame = get_kinect_frame()
        frame = cv2.resize(frame, (CAMERA_WIDTH, CAMERA_HEIGHT))

        # Draw interface
        draw_interface(frame, current_servo_x, current_servo_y)

        # Show frame
        cv2.imshow("Servo Calibration Tool", frame)
        cv2.setMouseCallback("Servo Calibration Tool", mouse_callback)

        # Handle keyboard input
        key = cv2.waitKey(1) & 0xFF

        if key == ord('q'):  # Save and quit
            if calibration_points:
                filename = save_calibration_data()
                print(f"✅ Calibration completed! Data saved to {filename}")
            else:
                print("❌ No calibration points to save!")
            break

        elif key == 27:  # ESC - quit without saving
            print("❌ Calibration cancelled")
            break

        elif key == ord('w'):  # Move up
            current_servo_y = min(SERVO_Y_MAX, current_servo_y + 1)
            move_servo(current_servo_x, current_servo_y)
            print(f"Servo moved to: X={current_servo_x}°, Y={current_servo_y}°")

        elif key == ord('s'):  # Move down
            current_servo_y = max(SERVO_Y_MIN, current_servo_y - 1)
            move_servo(current_servo_x, current_servo_y)
            print(f"Servo moved to: X={current_servo_x}°, Y={current_servo_y}°")

        elif key == ord('a'):  # Move left
            current_servo_x = max(SERVO_X_MIN, current_servo_x - 1)
            move_servo(current_servo_x, current_servo_y)
            print(f"Servo moved to: X={current_servo_x}°, Y={current_servo_y}°")

        elif key == ord('d'):  # Move right
            current_servo_x = min(SERVO_X_MAX, current_servo_x + 1)
            move_servo(current_servo_x, current_servo_y)
            print(f"Servo moved to: X={current_servo_x}°, Y={current_servo_y}°")

        elif key == ord(' '):  # Add calibration point
            # Use current mouse position
            calibration_point = {
                "camera": [mouse_x, mouse_y],
                "servo": [current_servo_x, current_servo_y]
            }
            calibration_points.append(calibration_point)
            print(f"✅ Added calibration point {len(calibration_points)}: Camera({mouse_x},{mouse_y}) -> Servo({current_servo_x}°,{current_servo_y}°)")

        elif key == ord('l'):  # Toggle laser
            laser_on = not laser_on
            laser.write(1 if laser_on else 0)
            print(f"🔴 Laser: {'ON' if laser_on else 'OFF'}")

        elif key == ord('r'):  # Reset calibration
            calibration_points = []
            print("🔄 Calibration data reset")

    # Cleanup
    laser.write(0)
    servo_x.write(88)  # Return to center
    servo_y.write(85)
    board.exit()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
